﻿using System;
using System.Collections;
using System.Collections.Generic;
using DG.Tweening;
using UnityEngine;
using UnityEngine.UI;
using Qarth;

namespace GameWish.Game
{
    public class GrassGamingPanel : AbstractPanel
    {
        [SerializeField] private Button m_BtnPlay;
        [SerializeField] private Button m_BtnBackHome;
        [SerializeField] private Text m_TxtDistanceShow;

        protected override void OnUIInit()
        {
            base.OnUIInit();

            m_BtnPlay.onClick.AddListener(OnClickPlay);
            m_BtnBackHome.onClick.AddListener(OnClickBackHome);


        }

        void FixedUpdate()
        {
            m_TxtDistanceShow.text = $"{GrassMgr.S.player.runDistance.ToString("F1")}M";
        }

        void OnClickPlay()
        {
            GrassMgr.S.StartGame();
            m_BtnPlay.gameObject.SetActive(false);
        }
        void OnClickBackHome()
        {
            FarmMgr.S.QuitGrassGame();
        }

        protected override void OnPanelOpen(params object[] args)
        {
            base.OnPanelOpen(args);
        }


        protected override void OnClose()
        {
            base.OnClose();

        }
    }
}