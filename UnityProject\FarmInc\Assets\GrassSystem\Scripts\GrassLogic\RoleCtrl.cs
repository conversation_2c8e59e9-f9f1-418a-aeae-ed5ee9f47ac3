
using UnityEngine;
using Qarth;
using DG.Tweening;


namespace GameWish.Game
{
	public class RoleCtrl : MonoBehaviour
	{
		public RoleAnimCtrl animCtrl;
		public CharacterController controller;
		public string m_JoystickName = "Movement";
		private float m_WalkSpdRatio = 0.3f;
		private Vector3 m_MoveDirection = Vector3.zero;
		private Vector3 m_LastMoveDirection = Vector3.zero;
		Vector3 m_InputVec;
		Vector3 m_MoveInput;

		public float m_MaxSpeed = 10;
		protected float m_RotSpd = 1000;
		protected float m_OriginMaxPd;

		private float m_RunDistance = 0f;
		public float runDistance
		{
			get { return m_RunDistance; }
		}


		public float curSpeed
		{
			get { return m_MoveDirection.magnitude * maxSpeed; }
		}

		public float maxSpeed
		{
			get { return m_MaxSpeed; }
			set { m_MaxSpeed = value; }
		}

		public float rotSpd
		{
			get { return m_RotSpd; }
		}

		public float maxSpdUpRatio
		{
			get { return maxSpeed / m_OriginMaxPd; }
		}

		protected bool m_Movable = false;

		public void Init()
		{
			m_Movable = true;
		}

		public void Stop()
		{
			m_Movable = false;
			animCtrl.PlayFall();
		}

		public void Reset()
		{
			this.transform.localPosition = new Vector3(0, 0, -15f);
			this.transform.localRotation = Quaternion.identity;
			animCtrl.ResetAnim();
			animCtrl.speed = 0;
			m_RunDistance = 0f;
		}

		public void SetMoveInput(Vector3 input)
		{
			m_MoveInput = input;
			m_LastMoveDirection = m_MoveDirection;
			m_MoveDirection = m_MoveInput;
		}

		public void SpeedUp(float duration)
		{
			//加速到固定值，再缓慢减速回来
			m_MaxSpeed = 8f;
			DOVirtual.DelayedCall(duration, () => { m_MaxSpeed = 5f; });
		}


		void Update()
		{
			if (!m_Movable) return;
			if (UltimateJoystick.GetJoystickState(m_JoystickName))
			{
				m_InputVec = new Vector3(UltimateJoystick.GetHorizontalAxis(m_JoystickName), 0,
					UltimateJoystick.GetVerticalAxis(m_JoystickName));

				//只保留左右偏移
				if (m_InputVec != Vector3.zero)
				{
					if (m_InputVec.z < 0.5)
					{
						m_InputVec.z = 0.5f;
					}
					if (m_InputVec.magnitude < 0.4f)
					{
						m_InputVec = m_InputVec.normalized * m_WalkSpdRatio;
					}
					else
					{
						m_InputVec = m_InputVec.normalized;
					}
				}

				SetMoveInput(m_InputVec);
			}
			else
				SetMoveInput(Vector3.forward);


			if (m_MoveDirection.sqrMagnitude * maxSpeed > 0)
			{
				animCtrl.speed = (curSpeed / maxSpeed);
			}
			else
			{
				animCtrl.speed = 0;
			}

		}

		Quaternion m_LookRot;
		Vector3 m_Fwd;
		void FixedUpdate()
		{
			if (!m_Movable) return;
			if (m_MoveInput == Vector3.zero)
				return;

			//转向
			m_Fwd = controller.transform.forward;
			m_LookRot = Quaternion.LookRotation(m_LastMoveDirection);
			if (Vector3.Dot(m_Fwd, m_LastMoveDirection) > 0.99)
				controller.transform.rotation = m_LookRot;
			else
				controller.transform.rotation = Quaternion.Slerp(controller.transform.rotation, m_LookRot, rotSpd * Time.fixedDeltaTime);

			//Log.e("grounded");
			controller.SimpleMove(m_MoveDirection * maxSpeed);
			m_RunDistance += m_MoveDirection.z * maxSpeed * Time.fixedDeltaTime;
			//Log.e(controller.isGrounded);


		}

		private GrassDoor m_CurHitDoor;
		private void OnControllerColliderHit(ControllerColliderHit hit)
		{
			if (hit.gameObject.CompareTag("GrassDoor"))
			{
				if (m_CurHitDoor == null || m_CurHitDoor.gameObject != hit.gameObject)
				{
					m_CurHitDoor = hit.gameObject.GetComponent<GrassDoor>();
				}
				m_CurHitDoor.OnHit();
			}
		}


	}



}
