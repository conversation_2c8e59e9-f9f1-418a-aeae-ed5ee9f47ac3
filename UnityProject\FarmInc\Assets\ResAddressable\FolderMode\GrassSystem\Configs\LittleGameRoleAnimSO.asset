%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ba586dd0e34f1b5429303733f8457430, type: 3}
  m_Name: LittleGameRoleAnimSO
  m_EditorClassIdentifier: 
  avatarMask: {fileID: 31900000, guid: bf0754bdcbeaa4e4f8d6fa607455280b, type: 2}
  idleClips:
  - {fileID: 7400000, guid: eae78ce9642505b46a6b611481702635, type: 2}
  walkClip: {fileID: 7400000, guid: cf9c0eea3a73d4d438b044c8d6dbe60a, type: 2}
  runClip: {fileID: 7400000, guid: 731936c45bfefa04899d0f76467e2484, type: 2}
  fallClip: {fileID: 7400000, guid: ae0c527e5a7065f4ebe252f5e26517e0, type: 2}
  normalMoveBlendTree:
    _FadeDuration: 0.25
    _Events:
      _NormalizedTimes: []
      _Callbacks: []
      _Names: []
    _Speed: 1
    _Animations:
    - {fileID: 7400000, guid: eae78ce9642505b46a6b611481702635, type: 2}
    - {fileID: 7400000, guid: cf9c0eea3a73d4d438b044c8d6dbe60a, type: 2}
    - {fileID: 7400000, guid: 731936c45bfefa04899d0f76467e2484, type: 2}
    _Speeds: []
    _SynchronizeChildren: 
    _Thresholds:
    - 0
    - 0.2
    - 1
    _DefaultParameter: 0
    _ExtrapolateSpeed: 1
