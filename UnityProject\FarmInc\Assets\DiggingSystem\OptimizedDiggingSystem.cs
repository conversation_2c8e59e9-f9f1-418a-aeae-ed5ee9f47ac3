using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
using Sirenix.OdinInspector;


namespace GameWish.Game
{
	public class OptimizedDiggingSystem : MonoBehaviour
	{
	    [System.Serializable]
	    public class DiggingSettings
	    {
	        [Header("Digging Parameters")]
	        public float innerRadius = 1f;
	        public float outerRadius = 2f;
	        public float digStrength = 1f;
	        public AnimationCurve falloffCurve = AnimationCurve.EaseInOut(0, 1, 1, 0);
	        
	        [Header("Performance")]
	        [Range(64, 1024)]
	        public int maskTextureSize = 512;
	        public bool useCommandBuffer = true;
	        public int maxDigsPerFrame = 5;
	    }
	
	    [System.Serializable]
	    public class RenderingSettings
	    {
	        [Header("Materials")]
	        public Material groundMaterial;
	        public Material digBrushMaterial;
	        
	        [Header("Textures")]
	        public Texture2D brushTexture;
	        public RenderTexture maskTexture;
	        
	        [Header("Rendering")]
	        public Camera maskCamera;
	        public LayerMask groundLayer = 1;
	    }
	
	    [SerializeField] private DiggingSettings diggingSettings = new DiggingSettings();
	    [SerializeField] private RenderingSettings renderingSettings = new RenderingSettings();
	    
	    [Header("Input")]
	    public Camera mainCamera;
	    public KeyCode digKey = KeyCode.Mouse0;
	    
	    // Performance optimization
	    private CommandBuffer commandBuffer;
	    private Queue<Vector3> pendingDigs = new Queue<Vector3>();
	    private Coroutine digProcessingCoroutine;
	    private MaterialPropertyBlock propertyBlock;
	    
	    // Shader property IDs (cached for performance)
	    private static readonly int MaskTexID = Shader.PropertyToID("_MaskTex");
	    private static readonly int BrushPosID = Shader.PropertyToID("_BrushPos");
	    private static readonly int BrushSizeID = Shader.PropertyToID("_BrushSize");
	    private static readonly int BrushStrengthID = Shader.PropertyToID("_BrushStrength");
	
	    private void Start()
	    {
	        InitializeSystem();
	    }
	
	    [Button("Initialize System")]
	    private void InitializeSystem()
	    {
	        // Create mask texture if not assigned
	        if (renderingSettings.maskTexture == null)
	        {
	            CreateMaskTexture();
	        }
	
	        // Initialize command buffer for batch rendering
	        if (diggingSettings.useCommandBuffer)
	        {
	            InitializeCommandBuffer();
	        }
	
	        // Initialize property block
	        propertyBlock = new MaterialPropertyBlock();
	
	        // Set initial mask texture to ground material
	        if (renderingSettings.groundMaterial != null)
	        {
	            renderingSettings.groundMaterial.SetTexture(MaskTexID, renderingSettings.maskTexture);
	        }
	
	        // Start dig processing coroutine
	        if (digProcessingCoroutine == null)
	        {
	            digProcessingCoroutine = StartCoroutine(ProcessDigsCoroutine());
	        }
	    }
	
	    private void CreateMaskTexture()
	    {
	        renderingSettings.maskTexture = new RenderTexture(
	            diggingSettings.maskTextureSize,
	            diggingSettings.maskTextureSize,
	            0,
	            RenderTextureFormat.R8
	        );
	        renderingSettings.maskTexture.name = "DiggingMask";
	        renderingSettings.maskTexture.filterMode = FilterMode.Bilinear;
	        renderingSettings.maskTexture.wrapMode = TextureWrapMode.Clamp;
	        
	        // Initialize with white (no holes)
	        RenderTexture.active = renderingSettings.maskTexture;
	        GL.Clear(true, true, Color.white);
	        RenderTexture.active = null;
	    }
	
	    private void InitializeCommandBuffer()
	    {
	        commandBuffer = new CommandBuffer();
	        commandBuffer.name = "DiggingSystem";
	        
	        if (renderingSettings.maskCamera != null)
	        {
	            renderingSettings.maskCamera.AddCommandBuffer(CameraEvent.BeforeForwardOpaque, commandBuffer);
	        }
	    }
	
	    private void Update()
	    {
	        HandleInput();
	    }
	
	    private void HandleInput()
	    {
	        if (Input.GetKey(digKey))
	        {
	            PerformDig();
	        }
	    }
	
	    private void PerformDig()
	    {
	        Ray ray = mainCamera.ScreenPointToRay(Input.mousePosition);
	        
	        if (Physics.Raycast(ray, out RaycastHit hit, Mathf.Infinity, renderingSettings.groundLayer))
	        {
	            // Add to pending digs queue for batch processing
	            pendingDigs.Enqueue(hit.point);
	        }
	    }
	
	    private IEnumerator ProcessDigsCoroutine()
	    {
	        while (true)
	        {
	            int processedThisFrame = 0;
	            
	            while (pendingDigs.Count > 0 && processedThisFrame < diggingSettings.maxDigsPerFrame)
	            {
	                Vector3 worldPos = pendingDigs.Dequeue();
	                ProcessSingleDig(worldPos);
	                processedThisFrame++;
	            }
	            
	            yield return null; // Wait for next frame
	        }
	    }
	
	    private void ProcessSingleDig(Vector3 worldPosition)
	    {
	        if (diggingSettings.useCommandBuffer)
	        {
	            ProcessDigWithCommandBuffer(worldPosition);
	        }
	        else
	        {
	            ProcessDigDirect(worldPosition);
	        }
	    }
	
	    private void ProcessDigWithCommandBuffer(Vector3 worldPosition)
	    {
	        // Convert world position to UV coordinates
	        Vector2 uv = WorldToUV(worldPosition);
	        
	        commandBuffer.Clear();
	        
	        // Set render target
	        commandBuffer.SetRenderTarget(renderingSettings.maskTexture);
	        
	        // Set material properties
	        propertyBlock.SetVector(BrushPosID, new Vector4(uv.x, uv.y, 0, 0));
	        propertyBlock.SetFloat(BrushSizeID, diggingSettings.innerRadius);
	        propertyBlock.SetFloat(BrushStrengthID, diggingSettings.digStrength);
	        
	        // Draw brush
	        if (renderingSettings.digBrushMaterial != null)
	        {
	            commandBuffer.DrawMesh(
	                GetQuadMesh(),
	                Matrix4x4.identity,
	                renderingSettings.digBrushMaterial,
	                0, 0,
	                propertyBlock
	            );
	        }
	        
	        // Execute command buffer
	        Graphics.ExecuteCommandBuffer(commandBuffer);
	    }
	
	    private void ProcessDigDirect(Vector3 worldPosition)
	    {
	        Vector2 uv = WorldToUV(worldPosition);
	        
	        RenderTexture previous = RenderTexture.active;
	        RenderTexture.active = renderingSettings.maskTexture;
	        
	        if (renderingSettings.digBrushMaterial != null)
	        {
	            renderingSettings.digBrushMaterial.SetVector(BrushPosID, new Vector4(uv.x, uv.y, 0, 0));
	            renderingSettings.digBrushMaterial.SetFloat(BrushSizeID, diggingSettings.innerRadius);
	            renderingSettings.digBrushMaterial.SetFloat(BrushStrengthID, diggingSettings.digStrength);
	            
	            Graphics.Blit(null, renderingSettings.maskTexture, renderingSettings.digBrushMaterial);
	        }
	        
	        RenderTexture.active = previous;
	    }
	
	    private Vector2 WorldToUV(Vector3 worldPosition)
	    {
	        // Convert world position to local position relative to this transform
	        Vector3 localPos = transform.InverseTransformPoint(worldPosition);
	        
	        // Convert to UV coordinates (assuming the ground plane is 1x1 unit)
	        Vector2 uv = new Vector2(
	            localPos.x + 0.5f,
	            localPos.z + 0.5f
	        );
	        
	        return uv;
	    }
	
	    private Mesh GetQuadMesh()
	    {
	        // Create a simple quad mesh for brush rendering
	        Mesh quad = new Mesh();
	        
	        Vector3[] vertices = new Vector3[4]
	        {
	            new Vector3(-0.5f, -0.5f, 0),
	            new Vector3(0.5f, -0.5f, 0),
	            new Vector3(-0.5f, 0.5f, 0),
	            new Vector3(0.5f, 0.5f, 0)
	        };
	        
	        Vector2[] uvs = new Vector2[4]
	        {
	            new Vector2(0, 0),
	            new Vector2(1, 0),
	            new Vector2(0, 1),
	            new Vector2(1, 1)
	        };
	        
	        int[] triangles = new int[6] { 0, 2, 1, 2, 3, 1 };
	        
	        quad.vertices = vertices;
	        quad.uv = uvs;
	        quad.triangles = triangles;
	        quad.RecalculateNormals();
	        
	        return quad;
	    }
	
	    [Button("Clear All Digs")]
	    public void ClearAllDigs()
	    {
	        if (renderingSettings.maskTexture != null)
	        {
	            RenderTexture.active = renderingSettings.maskTexture;
	            GL.Clear(true, true, Color.white);
	            RenderTexture.active = null;
	        }
	    }
	
	    [Button("Test Single Dig")]
	    public void TestSingleDig()
	    {
	        Vector3 testPos = transform.position;
	        pendingDigs.Enqueue(testPos);
	    }
	
	    private void OnDestroy()
	    {
	        // Cleanup
	        if (commandBuffer != null)
	        {
	            if (renderingSettings.maskCamera != null)
	            {
	                renderingSettings.maskCamera.RemoveCommandBuffer(CameraEvent.BeforeForwardOpaque, commandBuffer);
	            }
	            commandBuffer.Dispose();
	        }
	
	        if (renderingSettings.maskTexture != null)
	        {
	            renderingSettings.maskTexture.Release();
	        }
	
	        if (digProcessingCoroutine != null)
	        {
	            StopCoroutine(digProcessingCoroutine);
	        }
	    }
	
	    private void OnValidate()
	    {
	        // Ensure settings are valid
	        diggingSettings.innerRadius = Mathf.Max(0.1f, diggingSettings.innerRadius);
	        diggingSettings.outerRadius = Mathf.Max(diggingSettings.innerRadius, diggingSettings.outerRadius);
	        diggingSettings.maskTextureSize = Mathf.ClosestPowerOfTwo(diggingSettings.maskTextureSize);
	        diggingSettings.maxDigsPerFrame = Mathf.Max(1, diggingSettings.maxDigsPerFrame);
	    }
	}
	
}