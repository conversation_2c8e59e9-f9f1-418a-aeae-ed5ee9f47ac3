Shader "Hidden/AdditiveBlit"
{
    Properties
    {
        _MainTex ("Texture", 2D) = "black" {}
        _ClipShape ("Clip Shape", 2D) = "white" {}
        _ClipCenter ("Clip Center", Vector) = (0,0,0,0)
        _ClipScale ("Clip Scale", Float) = 1
    }
    
    SubShader
    {
        Tags { "RenderType"="Opaque" }
        
        Pass
        {
            Blend One One
            ZTest Always
            ZWrite Off
            Cull Off
            
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            
            #include "UnityCG.cginc"
            
            struct appdata
            {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
            };
            
            struct v2f
            {
                float2 uv : TEXCOORD0;
                float4 vertex : SV_POSITION;
            };
            
            sampler2D _MainTex;
            sampler2D _ClipShape;
            float4 _ClipCenter;
            float _ClipScale;
            
            v2f vert (appdata v)
            {
                v2f o;
                o.vertex = UnityObjectToClipPos(v.vertex);
                o.uv = v.uv;
                return o;
            }
            
            fixed4 frag (v2f i) : SV_Target
            {
                // 计算裁切形状的UV
                float2 clipUV = (i.uv - _ClipCenter.xy) * _ClipScale + 0.5;
                
                // 采样裁切形状
                fixed4 clipColor = tex2D(_ClipShape, clipUV);
                
                // 只使用红色通道
                return clipColor.r;
            }
            ENDCG
        }
    }
}