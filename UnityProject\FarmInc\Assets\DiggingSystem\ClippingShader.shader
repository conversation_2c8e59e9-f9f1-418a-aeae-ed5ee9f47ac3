Shader "Custom/ClippingShaderXY"
{
    Properties
    {
        _BaseColor("Base Color", Color) = (1,1,1,1)
        _BaseMap("Base Map", 2D) = "white" {}
        _ClippingMask("Clipping Mask", 2D) = "white" {}
        _ClipThreshold("Clip Threshold", Range(0, 1)) = 0.5
        _ClipScale("Clip Scale", Range(0.1, 5)) = 1
        _ClipCenter("Clip Center", Vector) = (0,0,0,0)
        [Toggle]_InvertClipping("Invert Clipping", Float) = 0
    }
    
    SubShader
    {
        Tags { "RenderType"="Opaque" "RenderPipeline"="UniversalPipeline" "Queue"="Geometry" }
        
        HLSLINCLUDE
        #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
        
        TEXTURE2D(_BaseMap);
        SAMPLER(sampler_BaseMap);
        TEXTURE2D(_ClippingMask);
        SAMPLER(sampler_ClippingMask);
        
        CBUFFER_START(UnityPerMaterial)
            float4 _BaseMap_ST;
            float4 _BaseColor;
            float4 _ClippingMask_ST;
            float _ClipThreshold;
            float _ClipScale;
            float4 _ClipCenter;
            float _InvertClipping;
        CBUFFER_END
        
        struct Attributes
        {
            float4 positionOS : POSITION;
            float2 uv : TEXCOORD0;
            float3 normalOS : NORMAL;
        };
        
        struct Varyings
        {
            float4 positionHCS : SV_POSITION;
            float2 uv : TEXCOORD0;
            float3 positionWS : TEXCOORD1;
            float3 normalWS : TEXCOORD2;
        };
        ENDHLSL
        
        Pass
        {
            Name "ForwardLit"
            Tags { "LightMode"="UniversalForward" }
            
            Cull Off
            
            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            
            Varyings vert(Attributes IN)
            {
                Varyings OUT;
                
                VertexPositionInputs vertexInput = GetVertexPositionInputs(IN.positionOS.xyz);
                OUT.positionHCS = vertexInput.positionCS;
                OUT.positionWS = vertexInput.positionWS;
                
                OUT.uv = TRANSFORM_TEX(IN.uv, _BaseMap);
                
                VertexNormalInputs normalInput = GetVertexNormalInputs(IN.normalOS);
                OUT.normalWS = normalInput.normalWS;
                
                return OUT;
            }
            
            half4 frag(Varyings IN) : SV_Target
            {
                // 计算裁切UV - 使用世界坐标XY平面
                float2 clipUV = (IN.positionWS.xy - _ClipCenter.xy) * _ClipScale;
                clipUV = TRANSFORM_TEX(clipUV, _ClippingMask);
                
                // 采样裁切遮罩
                float clipValue = SAMPLE_TEXTURE2D(_ClippingMask, sampler_ClippingMask, clipUV).r;
                
                // 裁切判断
                if (_InvertClipping > 0.5)
                {
                    clip(clipValue - _ClipThreshold);
                }
                else
                {
                    clip(_ClipThreshold - clipValue);
                }
                
                // 采样基础纹理
                half4 baseColor = SAMPLE_TEXTURE2D(_BaseMap, sampler_BaseMap, IN.uv) * _BaseColor;
                
                // 简单光照计算
                float3 lightDir = normalize(_MainLightPosition.xyz);
                float NdotL = max(0, dot(IN.normalWS, lightDir));
                float3 shading = NdotL * _MainLightColor.rgb;
                
                return baseColor * float4(shading, 1);
            }
            ENDHLSL
        }
    }
}