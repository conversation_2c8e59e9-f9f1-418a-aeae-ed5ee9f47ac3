using Qarth;
using UnityEngine;
using GameWish.Game;
using TMPro;


public class GrassRollWood : MonoBehaviour
{
    public float moveSpd = 3f;
    private bool m_Initialized = false;

    public void Init()
    {
        this.CallWithDelay(() => { m_Initialized = true; }, 2f);
        //m_Initialized = true;
    }

    public void Stop()
    {
        m_Initialized = false;
    }

    public void Reset()
    {
        this.transform.SetLocalZ(-22.5f);
    }

    private void OnTriggerEnter(Collider other)
    {
        if (FarmMgr.S.IsTriggerPlayer(other.gameObject))
        {
            //游戏结束
            GrassMgr.S.GameOver();
            //弹出结算界面
            this.CallWithDelay(() =>
            {
                UIMgr.S.OpenPanel(UIID.GrassOverPanel);
            }, 1f);
        }
    }

    void FixedUpdate()
    {
        if (!m_Initialized) return;
        transform.position += Vector3.forward * moveSpd * Time.fixedDeltaTime;
    }
}