﻿using System;
using System.Collections;
using System.Collections.Generic;
using DG.Tweening;
using UnityEngine;
using UnityEngine.UI;
using Qarth;
using Unity.Mathematics;

namespace GameWish.Game
{
    public class GrassOverPanel : AbstractPanel
    {
        [SerializeField] private Text m_TxtDistanceShow;
        [SerializeField] private Text m_TxtDiamondShow;


        [SerializeField] private Button m_BtnBack;
        [SerializeField] private Button m_BtnRestart;

        private int diamondCount = 0;

        protected override void OnUIInit()
        {
            base.OnUIInit();

            m_BtnBack.onClick.AddListener(OnClickBack);
            m_BtnRestart.onClick.AddListener(OnClickRestart);

        }

        protected override void OnPanelOpen(params object[] args)
        {
            base.OnPanelOpen(args);
            OpenDependPanel(EngineUI.MaskPanel, -1);
            m_TxtDistanceShow.text = $"{GrassMgr.S.player.runDistance.ToString("F1")}m";
            int door = (int)(GrassMgr.S.player.runDistance / 40) + 1;
            diamondCount = (int)((math.log10(door) / math.log10(1.5f)) * 10);
            //显示奖励数量
            m_TxtDiamondShow.text = $"x{diamondCount}";
        }

        protected override void OnClose()
        {
            base.OnClose();

        }

        public override BackKeyCodeResult OnBackKeyDown()
        {
            OnClickBack();
            return BackKeyCodeResult.PROCESS_AND_BLOCK;
        }

        void OnClickBack()
        {
            FarmMgr.S.QuitGrassGame(() =>
            {
                PackMgr.S.AddPlayerItem(diamondCount, ItemTypeEnum.Gem, "grass");
                WorldNormalUIPanel.S.ShowPropReward(FarmMgr.S.TrsPlayer.position, ItemTypeEnum.Gem, diamondCount);
            });
            CloseSelfPanel();
        }
        void OnClickRestart()
        {
            AdsPlayMgr.S.PlayRewardAd("Grass_Restart", (clicked) =>
            {
                PackMgr.S.AddPlayerItem(diamondCount, ItemTypeEnum.Gem, "grass");
                WorldNormalUIPanel.S.ShowPropReward(GrassMgr.S.playerTransform.position, ItemTypeEnum.Gem, diamondCount);
                CloseSelfPanel();
                GrassMgr.S.Reset();
                DOVirtual.DelayedCall(1f, () =>
                {
                    GrassMgr.S.StartGame();
                });

            }, null);
        }

    }
}