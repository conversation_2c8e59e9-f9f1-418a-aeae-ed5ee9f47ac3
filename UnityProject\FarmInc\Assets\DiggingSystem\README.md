# URP优化挖掘系统

这是一个针对Unity URP项目优化的高性能挖掘系统，支持实时地形挖掘效果。

## 系统组件

### 1. Shader文件
- **URPDiggingGround.shader**: URP兼容的地面材质Shader，支持遮罩纹理挖洞
- **URPDiggingBrush.shader**: 挖掘笔刷Shader，用于在遮罩纹理上绘制挖掘效果
- **DiggingCompute.compute**: Compute Shader版本，提供最高性能

### 2. 脚本文件
- **OptimizedDiggingSystem.cs**: 基础优化版本，使用CommandBuffer批处理
- **ComputeDiggingSystem.cs**: 高性能版本，使用Compute Shader批处理
- **TestDigging.cs**: 原始测试脚本（保留用于参考）

## 设置步骤

### 基础设置（OptimizedDiggingSystem）

1. **创建地面对象**
   - 创建一个Plane或Quad作为地面
   - 应用使用URPDiggingGround.shader的材质

2. **配置OptimizedDiggingSystem**
   - 将脚本添加到场景中的空GameObject上
   - 设置以下参数：
     - Ground Material: 使用URPDiggingGround.shader的材质
     - Dig Brush Material: 使用URPDiggingBrush.shader的材质
     - Main Camera: 场景主摄像机
     - Brush Texture: 笔刷纹理（可选，默认圆形）

3. **调整挖掘参数**
   - Inner Radius: 内圈挖掘半径
   - Outer Radius: 外圈影响半径
   - Dig Strength: 挖掘强度
   - Mask Texture Size: 遮罩纹理分辨率（建议512-1024）

### 高性能设置（ComputeDiggingSystem）

1. **前置条件**
   - 确保目标平台支持Compute Shader
   - GPU支持Shader Model 4.5或更高

2. **配置ComputeDiggingSystem**
   - 将脚本添加到场景中的空GameObject上
   - 设置Digging Compute字段为DiggingCompute.compute
   - 配置Ground Material使用URPDiggingGround.shader
   - 设置Brush Texture（推荐使用圆形渐变纹理）

3. **性能调优**
   - Mask Texture Size: 1024（高质量）或512（平衡性能）
   - Max Digs Per Batch: 16-32（根据GPU性能调整）
   - Batches Per Frame: 1-3（避免帧率下降）

## 性能优化特性

### 1. 批处理系统
- 将多个挖掘操作合并为批次处理
- 减少GPU调用次数
- 支持每帧处理数量限制

### 2. Compute Shader加速
- 并行处理大量挖掘操作
- GPU加速的纹理操作
- 支持复杂的笔刷效果

### 3. 内存优化
- 使用对象池减少GC压力
- 缓存Shader属性ID
- 智能纹理格式选择

### 4. 渲染优化
- CommandBuffer批处理
- 最小化状态切换
- 支持URP渲染管线特性

## 使用建议

### 移动平台优化
- 使用较小的遮罩纹理（256-512）
- 限制每帧挖掘数量（1-2个批次）
- 考虑使用简化的笔刷纹理

### PC/主机平台
- 可以使用更大的遮罩纹理（1024-2048）
- 支持更多并发挖掘操作
- 启用高质量笔刷效果

### VR平台
- 优先考虑性能稳定性
- 使用固定帧率限制
- 简化视觉效果以保持90fps

## 扩展功能

### 自定义笔刷
- 支持不同形状的笔刷纹理
- 可配置的衰减曲线
- 动态笔刷大小调整

### 多层挖掘
- 支持不同深度的挖掘效果
- 可配置的材质混合
- 分层渲染支持

### 物理集成
- 与Unity物理系统集成
- 动态碰撞体更新
- 粒子效果触发

## 故障排除

### 常见问题
1. **挖掘效果不显示**
   - 检查材质是否正确设置
   - 确认遮罩纹理已分配
   - 验证Shader编译无错误

2. **性能问题**
   - 降低遮罩纹理分辨率
   - 减少每帧处理的批次数量
   - 检查GPU兼容性

3. **Compute Shader错误**
   - 确认平台支持Compute Shader
   - 检查Shader Model版本
   - 验证缓冲区大小设置

### 调试工具
- 使用Performance Stats按钮监控性能
- 启用Unity Profiler查看GPU使用情况
- 检查Frame Debugger了解渲染流程

## 版本兼容性
- Unity 2022.3 LTS或更高版本
- URP 14.0或更高版本
- 支持的平台：PC, Mac, Linux, Android, iOS, Console
