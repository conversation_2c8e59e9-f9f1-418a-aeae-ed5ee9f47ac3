using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Rendering;
using Sirenix.OdinInspector;


namespace GameWish.Game
{
	public class ComputeDiggingSystem : MonoBehaviour
	{
	    [System.Serializable]
	    public struct DigData
	    {
	        public Vector2 position;
	        public float size;
	        public float strength;
	        
	        public DigData(Vector2 pos, float s, float str)
	        {
	            position = pos;
	            size = s;
	            strength = str;
	        }
	        
	        public Vector4 ToVector4()
	        {
	            return new Vector4(position.x, position.y, size, strength);
	        }
	    }
	
	    [System.Serializable]
	    public class ComputeSettings
	    {
	        [Header("Performance")]
	        [Range(64, 2048)]
	        public int maskTextureSize = 1024;
	        
	        [Range(1, 64)]
	        public int maxDigsPerBatch = 32;
	        
	        [Range(1, 10)]
	        public int batchesPerFrame = 2;
	        
	        [Header("Digging Parameters")]
	        public float defaultDigSize = 0.05f;
	        public float defaultDigStrength = 1f;
	        public float brushFalloff = 2f;
	        
	        [Header("Compute Shader")]
	        public ComputeShader diggingCompute;
	        public Texture2D brushTexture;
	    }
	
	    [System.Serializable]
	    public class RenderSettings
	    {
	        [Header("Materials & Textures")]
	        public Material groundMaterial;
	        public RenderTexture maskTexture;
	        
	        [Header("Input")]
	        public Camera mainCamera;
	        public LayerMask groundLayer = 1;
	        public KeyCode digKey = KeyCode.Mouse0;
	    }
	
	    [SerializeField] private ComputeSettings computeSettings = new ComputeSettings();
	    [SerializeField] private RenderSettings renderSettings = new RenderSettings();
	    
	    // Compute shader data
	    private ComputeBuffer digBuffer;
	    private List<DigData> pendingDigs = new List<DigData>();
	    private Queue<List<DigData>> digBatches = new Queue<List<DigData>>();
	    
	    // Compute shader kernel and property IDs
	    private int kernelIndex;
	    private int maskTextureID;
	    private int brushTextureID;
	    private int digPositionsID;
	    private int digCountID;
	    private int textureSizeID;
	    private int brushFalloffID;
	    
	    // Performance monitoring
	    [ShowInInspector, ReadOnly] private int totalDigsProcessed = 0;
	    [ShowInInspector, ReadOnly] private int currentBatchSize = 0;
	    [ShowInInspector, ReadOnly] private int pendingBatchCount = 0;
	
	    private void Start()
	    {
	        InitializeComputeSystem();
	    }
	
	    [Button("Initialize System")]
	    private void InitializeComputeSystem()
	    {
	        // Validate compute shader
	        if (computeSettings.diggingCompute == null)
	        {
	            Debug.LogError("Digging Compute Shader is not assigned!");
	            return;
	        }
	
	        // Get kernel index
	        kernelIndex = computeSettings.diggingCompute.FindKernel("CSMain");
	        
	        // Cache property IDs
	        maskTextureID = Shader.PropertyToID("MaskTexture");
	        brushTextureID = Shader.PropertyToID("BrushTexture");
	        digPositionsID = Shader.PropertyToID("DigPositions");
	        digCountID = Shader.PropertyToID("DigCount");
	        textureSizeID = Shader.PropertyToID("TextureSize");
	        brushFalloffID = Shader.PropertyToID("BrushFalloff");
	
	        // Create mask texture
	        CreateMaskTexture();
	        
	        // Create compute buffer
	        CreateComputeBuffer();
	        
	        // Set static compute shader parameters
	        SetStaticComputeParameters();
	        
	        // Assign mask texture to ground material
	        if (renderSettings.groundMaterial != null)
	        {
	            renderSettings.groundMaterial.SetTexture("_MaskTex", renderSettings.maskTexture);
	        }
	
	        Debug.Log("Compute Digging System initialized successfully!");
	    }
	
	    private void CreateMaskTexture()
	    {
	        if (renderSettings.maskTexture != null)
	        {
	            renderSettings.maskTexture.Release();
	        }
	
	        renderSettings.maskTexture = new RenderTexture(
	            computeSettings.maskTextureSize,
	            computeSettings.maskTextureSize,
	            0,
	            RenderTextureFormat.RFloat
	        );
	        
	        renderSettings.maskTexture.enableRandomWrite = true;
	        renderSettings.maskTexture.filterMode = FilterMode.Bilinear;
	        renderSettings.maskTexture.wrapMode = TextureWrapMode.Clamp;
	        renderSettings.maskTexture.Create();
	
	        // Initialize with white (no holes)
	        ClearMaskTexture();
	    }
	
	    private void CreateComputeBuffer()
	    {
	        if (digBuffer != null)
	        {
	            digBuffer.Release();
	        }
	
	        digBuffer = new ComputeBuffer(computeSettings.maxDigsPerBatch, sizeof(float) * 4);
	    }
	
	    private void SetStaticComputeParameters()
	    {
	        computeSettings.diggingCompute.SetTexture(kernelIndex, maskTextureID, renderSettings.maskTexture);
	        computeSettings.diggingCompute.SetTexture(kernelIndex, brushTextureID, computeSettings.brushTexture);
	        computeSettings.diggingCompute.SetInt(textureSizeID, computeSettings.maskTextureSize);
	        computeSettings.diggingCompute.SetFloat(brushFalloffID, computeSettings.brushFalloff);
	    }
	
	    private void Update()
	    {
	        HandleInput();
	        ProcessDigBatches();
	    }
	
	    private void HandleInput()
	    {
	        if (Input.GetKey(renderSettings.digKey))
	        {
	            PerformDig();
	        }
	    }
	
	    private void PerformDig()
	    {
	        Ray ray = renderSettings.mainCamera.ScreenPointToRay(Input.mousePosition);
	        
	        if (Physics.Raycast(ray, out RaycastHit hit, Mathf.Infinity, renderSettings.groundLayer))
	        {
	            Vector2 uv = WorldToUV(hit.point);
	            DigData digData = new DigData(uv, computeSettings.defaultDigSize, computeSettings.defaultDigStrength);
	            
	            AddDigToBatch(digData);
	        }
	    }
	
	    private void AddDigToBatch(DigData digData)
	    {
	        pendingDigs.Add(digData);
	        
	        // Create batch when we reach max size
	        if (pendingDigs.Count >= computeSettings.maxDigsPerBatch)
	        {
	            CreateBatch();
	        }
	    }
	
	    private void CreateBatch()
	    {
	        if (pendingDigs.Count > 0)
	        {
	            List<DigData> batch = new List<DigData>(pendingDigs);
	            digBatches.Enqueue(batch);
	            pendingDigs.Clear();
	            
	            pendingBatchCount = digBatches.Count;
	        }
	    }
	
	    private void ProcessDigBatches()
	    {
	        int batchesProcessed = 0;
	        
	        while (digBatches.Count > 0 && batchesProcessed < computeSettings.batchesPerFrame)
	        {
	            List<DigData> batch = digBatches.Dequeue();
	            ProcessBatchWithCompute(batch);
	            batchesProcessed++;
	            
	            totalDigsProcessed += batch.Count;
	        }
	        
	        // Process remaining pending digs if any
	        if (pendingDigs.Count > 0 && digBatches.Count == 0)
	        {
	            CreateBatch();
	        }
	        
	        pendingBatchCount = digBatches.Count;
	        currentBatchSize = pendingDigs.Count;
	    }
	
	    private void ProcessBatchWithCompute(List<DigData> batch)
	    {
	        if (batch.Count == 0) return;
	
	        // Convert to Vector4 array for compute buffer
	        Vector4[] digDataArray = new Vector4[batch.Count];
	        for (int i = 0; i < batch.Count; i++)
	        {
	            digDataArray[i] = batch[i].ToVector4();
	        }
	
	        // Upload data to compute buffer
	        digBuffer.SetData(digDataArray);
	        
	        // Set compute shader parameters
	        computeSettings.diggingCompute.SetBuffer(kernelIndex, digPositionsID, digBuffer);
	        computeSettings.diggingCompute.SetInt(digCountID, batch.Count);
	
	        // Calculate thread groups
	        int threadGroups = Mathf.CeilToInt(computeSettings.maskTextureSize / 8.0f);
	        
	        // Dispatch compute shader
	        computeSettings.diggingCompute.Dispatch(kernelIndex, threadGroups, threadGroups, 1);
	    }
	
	    private Vector2 WorldToUV(Vector3 worldPosition)
	    {
	        Vector3 localPos = transform.InverseTransformPoint(worldPosition);
	        return new Vector2(localPos.x + 0.5f, localPos.z + 0.5f);
	    }
	
	    [Button("Clear All Digs")]
	    public void ClearMaskTexture()
	    {
	        if (renderSettings.maskTexture != null)
	        {
	            RenderTexture previous = RenderTexture.active;
	            RenderTexture.active = renderSettings.maskTexture;
	            GL.Clear(true, true, Color.white);
	            RenderTexture.active = previous;
	        }
	        
	        // Clear pending operations
	        pendingDigs.Clear();
	        digBatches.Clear();
	        totalDigsProcessed = 0;
	        currentBatchSize = 0;
	        pendingBatchCount = 0;
	    }
	
	    [Button("Test Batch Dig")]
	    public void TestBatchDig()
	    {
	        for (int i = 0; i < 10; i++)
	        {
	            Vector2 randomUV = new Vector2(
	                Random.Range(0.2f, 0.8f),
	                Random.Range(0.2f, 0.8f)
	            );
	            
	            DigData digData = new DigData(randomUV, computeSettings.defaultDigSize, computeSettings.defaultDigStrength);
	            AddDigToBatch(digData);
	        }
	    }
	
	    private void OnDestroy()
	    {
	        // Cleanup compute buffer
	        if (digBuffer != null)
	        {
	            digBuffer.Release();
	            digBuffer = null;
	        }
	
	        // Cleanup render texture
	        if (renderSettings.maskTexture != null)
	        {
	            renderSettings.maskTexture.Release();
	        }
	    }
	
	    private void OnValidate()
	    {
	        // Ensure valid settings
	        computeSettings.maskTextureSize = Mathf.ClosestPowerOfTwo(computeSettings.maskTextureSize);
	        computeSettings.maxDigsPerBatch = Mathf.Max(1, computeSettings.maxDigsPerBatch);
	        computeSettings.batchesPerFrame = Mathf.Max(1, computeSettings.batchesPerFrame);
	        computeSettings.defaultDigSize = Mathf.Max(0.001f, computeSettings.defaultDigSize);
	        computeSettings.brushFalloff = Mathf.Max(0.1f, computeSettings.brushFalloff);
	    }
	
	    // Performance monitoring methods
	    [Button("Show Performance Stats")]
	    private void ShowPerformanceStats()
	    {
	        Debug.Log($"=== Digging System Performance ===\n" +
	                  $"Total Digs Processed: {totalDigsProcessed}\n" +
	                  $"Current Batch Size: {currentBatchSize}\n" +
	                  $"Pending Batches: {pendingBatchCount}\n" +
	                  $"Mask Texture Size: {computeSettings.maskTextureSize}x{computeSettings.maskTextureSize}\n" +
	                  $"Max Digs Per Batch: {computeSettings.maxDigsPerBatch}\n" +
	                  $"Batches Per Frame: {computeSettings.batchesPerFrame}");
	    }
	}
	
}