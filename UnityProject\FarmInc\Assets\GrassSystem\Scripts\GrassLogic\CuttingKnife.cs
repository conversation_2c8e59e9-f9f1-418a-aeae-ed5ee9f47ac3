using System.Collections.Generic;
using GameWish.Game;
using UnityEngine;

public class CuttingKnife : MonoBehaviour
{
    public List<GrassLand> mowGrass;


    public float cutSize = 1f;
    private bool m_AsOne;
    private Transform m_TransPlayer;


    public void Init(bool asOne, Transform transPlayer)
    {
        m_AsOne = asOne;
        m_TransPlayer = transPlayer;
        Reset();
        mowGrass = GrassMgr.S.grassLands;
        mowGrass.ForEach(land => land.OnGrassMowed += OnMowGrass);
    }

    private void Reset()
    {
        mowGrass.ForEach(land => land.ClearMowEvent());
    }

    private void OnDestroy()
    {
        mowGrass.ForEach(land => land.ClearMowEvent());
    }


    void FixedUpdate()
    {
        if (m_AsOne) return;
        mowGrass.ForEach(land => land.MakeGrassDisappearInArea(transform.position, cutSize));

    }

    private float m_LastPlayEffectTime = -1;
    private Vector3 m_LastPlayEffectPos;
    private void OnMowGrass(Vector3 position, int curNum, int remainNum)
    {

        // if (Time.time - m_LastPlayEffectTime < 0.05f) return;
        if (m_AsOne)
        {
            mowGrass[0].SpawnCuttingParticle(this.transform.position + m_TransPlayer.forward * cutSize * 0.5f, Color.white);
        }
        else
        {
            if (m_LastPlayEffectPos == position) return;
            mowGrass[0].SpawnCuttingParticle(position + m_TransPlayer.forward * cutSize * 0.5f, Color.white);
        }

        m_LastPlayEffectTime = Time.time;
        m_LastPlayEffectPos = position;
    }
}
