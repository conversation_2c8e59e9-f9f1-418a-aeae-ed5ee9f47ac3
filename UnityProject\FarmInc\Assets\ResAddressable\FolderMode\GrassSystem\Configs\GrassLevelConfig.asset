%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5f5b616f76882dd45a97eff45cef05e4, type: 3}
  m_Name: GrassLevelConfig
  m_EditorClassIdentifier: 
  levels:
  - level: 1
    doorConfigs:
    - function: 3
      value: 2
      hp: 10
    - function: 1
      value: 2
      hp: 10
    bubbleConfigs:
    - function: 1
      value: 2
    - function: 0
      value: 0
  - level: 2
    doorConfigs:
    - function: 3
      value: 2
      hp: 80
    - function: 1
      value: 2
      hp: 80
    bubbleConfigs:
    - function: 1
      value: 3
    - function: 2
      value: 1
  - level: 0
    doorConfigs:
    - function: 1
      value: 1
      hp: 160
    - function: 0
      value: 0
      hp: 20
    bubbleConfigs:
    - function: 0
      value: 0
    - function: 3
      value: 0
  - level: 0
    doorConfigs:
    - function: 2
      value: 2
      hp: 400
    - function: 3
      value: 2
      hp: 600
    bubbleConfigs:
    - function: 2
      value: 1
  - level: 0
    doorConfigs:
    - function: 1
      value: 3
      hp: 1000
    - function: 1
      value: 5
      hp: 1300
    bubbleConfigs: []
  - level: 0
    doorConfigs:
    - function: 2
      value: 5
      hp: 2000
    - function: 3
      value: 2
      hp: 1000
    bubbleConfigs:
    - function: 0
      value: 0
    - function: 1
      value: 4
