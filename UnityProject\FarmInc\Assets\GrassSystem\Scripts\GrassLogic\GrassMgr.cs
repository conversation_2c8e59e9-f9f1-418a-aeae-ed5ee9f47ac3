using System.Text;
using Qarth;
using UnityEngine;
using GameWish.Game;
using System.Collections.Generic;
using Sirenix.OdinInspector;


public class GrassMgr : TMonoSingleton<GrassMgr>
{
    //三块地无限循环，草地管理器
    public List<GrassLand> grassLands = new List<GrassLand>();
    public KnifeCtrl knifeCtrl;
    public Transform playerTransform;
    public RoleCtrl player;
    public GrassRollWood rollWood;
    private int currentLandIndex = 0;
    private int m_RoadCount = 3;
    public float zOffset = 40f; //每块地的长度

    private float lastPlayerZ;

    public GrassLevelConfig levelConfig;


    // 0  20  40
    [Button("StartGame")]
    public void StartGame()
    {
        rollWood.Init();
        player.Init();
    }

    public void GameOver()
    {
        rollWood.Stop();
        player.Stop();
        knifeCtrl.Stop();

    }
    [Button("Reset")]
    public void Reset()
    {
        rollWood.Reset();
        player.Reset();
        knifeCtrl.Reset();
        //草地重置
        List<float> pos = new List<float>() { 0, 40, 80 };
        for (int i = 0; i < grassLands.Count; i++)
        {
            float offset = pos[i] - grassLands[i].transform.position.z;
            grassLands[i].transform.position = new Vector3(0, 0, pos[i]);
            grassLands[i].ResetAllGrass(offset, levelConfig.levels[i]);
        }
        currentLandIndex = Mathf.FloorToInt(playerTransform.position.z / zOffset);
        m_RoadCount = 3;
    }

    private void Start()
    {
        for (int i = 0; i < grassLands.Count; i++)
        {
            grassLands[i].Initialize(levelConfig.levels[i]);
        }
    }


    private void Update()
    {
        if (playerTransform == null)
        {
            return;
        }

        int index = Mathf.FloorToInt(playerTransform.position.z / zOffset);
        if (index > currentLandIndex)
        {
            currentLandIndex = index;
            UpdateGrassLands();
        }
    }

    private void UpdateGrassLands()
    {
        //把走过的地块移到后面

        for (int i = 0; i < grassLands.Count; i++)
        {
            if (i < currentLandIndex)
            {
                GrassLand land = grassLands[i];
                Vector3 newPos = new Vector3(0, 0, m_RoadCount * zOffset);
                land.transform.position = newPos;
                if (currentLandIndex + 1 >= levelConfig.levels.Count)
                {
                    land.ReShowAllGrass(zOffset, levelConfig.levels[levelConfig.levels.Count - 1]);
                }
                else
                {
                    land.ReShowAllGrass(zOffset, levelConfig.levels[currentLandIndex + 1]);
                }

                grassLands.RemoveAt(i);
                grassLands.Add(land);
                m_RoadCount++;
                break;
            }
        }

    }




}