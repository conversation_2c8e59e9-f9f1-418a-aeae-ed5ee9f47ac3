using Animancer;
using Sirenix.OdinInspector;
using UnityEngine;


namespace GameWish.Game
{
	public class RoleAnimCtrl : MonoBehaviour
	{
		[SerializeField] protected LittleGameRoleAnimSO m_AnimConfig;
		[SerializeField] protected AnimancerComponent m_AnimComp;
		protected AnimancerLayer m_UpperLayer;
		protected AnimancerLayer m_BaseLayer;

		[Range(0, 1)] public float speed;
		public LinearMixerState curState;
		public LittleGameRoleAnimSO animConfig => m_AnimConfig;


		private void Start()
		{
			// 创建线性混合状态
			var _mixer = m_AnimConfig.normalMoveBlendTree.CreateState();
			curState = _mixer as LinearMixerState;
			curState.Parameter = speed;                          // 播放混合状态
			m_AnimComp.Play(curState);
			m_BaseLayer = m_AnimComp.Layers[0];
			m_BaseLayer.SetDebugName("BaseLayer");
			m_UpperLayer = m_AnimComp.Layers[1];
			m_UpperLayer.SetDebugName("UpperLayer");
			m_UpperLayer.SetMask(m_AnimConfig.avatarMask);
		}

		private void Update()
		{
			curState.Parameter = speed;
		}

		public void PlayUpperAnim(AnimationClip clip)
		{
			m_UpperLayer.Play(clip);
		}

		public void PlayBaseAnim(AnimationClip clip)
		{
			m_BaseLayer.Play(clip);
		}

		public void PlayFall()
		{
			PlayBaseAnim(m_AnimConfig.fallClip);
		}
		public void ResetAnim()
		{
			m_AnimComp.Play(curState);
		}



	}

}