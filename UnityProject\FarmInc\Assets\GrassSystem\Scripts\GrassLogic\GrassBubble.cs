using Qarth;
using UnityEngine;
using GameWish.Game;
using TMPro;
using System.Collections.Generic;


public class GrassBubble : MonoBehaviour
{
    [SerializeField] GameObject m_GoKnifeRoot;
    [SerializeField] List<GameObject> m_GoKnifes;
    [SerializeField] GameObject m_GoSpdUp;
    private GrassBubbleConfig m_Config;
    private TextMeshPro m_Text;
    public void Init(GrassBubbleConfig config)
    {
        m_Config = config;
        if (m_Config.function == BubbleFunction.Empty)
        {
            this.gameObject.SetActive(false);
            return;
        }
        this.gameObject.SetActive(true);
        if (m_Text == null)
        {
            m_Text = GetComponentInChildren<TextMeshPro>();
        }
        m_GoKnifeRoot.SetActive(false);
        m_GoSpdUp.SetActive(false);
        m_Text.text = "";
        switch (m_Config.function)
        {
            case BubbleFunction.UpgradeKnife:
                m_GoKnifeRoot.SetActive(true);
                m_GoKnifes.ForEach(go => go.SetActive(false));
                m_GoKnifes[m_Config.value - 1].SetActive(true);
                break;
            case BubbleFunction.SpdUp:
                m_GoSpdUp.SetActive(true);
                break;
            default:
                m_GoKnifeRoot.SetActive(true);
                m_GoKnifes.ForEach(go => go.SetActive(false));
                m_GoKnifes[GrassMgr.S.knifeCtrl.knifeId - 1].SetActive(true);
                m_Text.text = m_Config.bubbleShow;
                break;
        }

    }

    private void OnTriggerEnter(Collider other)
    {
        if (FarmMgr.S.IsTriggerPlayer(other.gameObject))
        {
            AudioMgr.S.PlaySound(AudioID.AUDIO_UP);
            //作用生效
            m_Config?.Work();
            //销毁气泡
            this.gameObject.SetActive(false);
        }
    }

}