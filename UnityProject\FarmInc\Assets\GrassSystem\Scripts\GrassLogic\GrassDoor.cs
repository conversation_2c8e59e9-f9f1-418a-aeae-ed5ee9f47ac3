using Qarth;
using UnityEngine;
using GameWish.Game;
using TMPro;
using DG.Tweening;
using System.Collections.Generic;


public class GrassDoor : MonoBehaviour
{
    [SerializeField] private Transform m_TranRenderer;
    [SerializeField] private Transform m_EffectRoot;
    [SerializeField] private TextMeshPro m_TextShow;
    [SerializeField] private TextMeshPro m_TextHp;
    [SerializeField] GameObject m_GoKnifeRoot;
    [SerializeField] List<GameObject> m_GoKnifes;
    private GrassDoorConfig m_Config;

    private float m_CurHp = 100;
    private ParticleSystem[] m_Effects;
    public void Init(GrassDoorConfig config)
    {
        m_Config = config;
        m_CurHp = m_Config.hp;
        if (m_Config.function == DoorFunction.Empty)
        {
            this.gameObject.SetActive(false);
            return;
        }
        this.gameObject.SetActive(true);


        m_TextShow.text = m_Config.doorShow;
        m_TextHp.text = $"HP:{m_CurHp}";
        m_EnterTime = 0;
        if (m_Effects == null || m_Effects.Length == 0)
        {
            m_Effects = m_EffectRoot.GetComponentsInChildren<ParticleSystem>();
        }
        foreach (var effect in m_Effects)
        {
            effect.Stop();
        }

        OnKnifeChange(0, GrassMgr.S.knifeCtrl.knifeId);
    }

    void OnEnable()
    {

        EventSystem.S.Register(EventID.OnGrassKnifeChange, OnKnifeChange);
    }

    void OnDisable()
    {
        EventSystem.S.UnRegister(EventID.OnGrassKnifeChange, OnKnifeChange);
    }

    void OnKnifeChange(int key, params object[] args)
    {
        int id = (int)args[0];
        m_GoKnifeRoot.SetActive(true);
        m_GoKnifes.ForEach(go => go.SetActive(false));
        m_GoKnifes[id - 1].SetActive(true);
    }



    //根据接触时间去计算掉血
    float m_EnterTime = 0;
    public void OnHit()
    {
        float damage = ((Time.deltaTime) * GrassMgr.S.knifeCtrl.power);
        m_CurHp -= damage;
        if (m_CurHp <= 0)
        {
            m_Config?.Work();
            this.gameObject.SetActive(false);
        }
        PlayHitAnim();

    }

    private void OnTriggerEnter(Collider other)
    {
        if (other.CompareTag("GrassRollWood"))
        {
            //直接破碎
            this.gameObject.SetActive(false);
        }
    }
    float m_LastPlayAnimTIme = -1f;
    private float m_InitScale = 6f;
    private void PlayHitAnim()
    {
        if (Time.time - m_LastPlayAnimTIme < 0.2f) return;
        AudioMgr.S.PlaySound(AudioID.AUDIO_CHOP);
        m_LastPlayAnimTIme = Time.time;
        m_TranRenderer.DOKill();
        m_TranRenderer.localScale = Vector3.one * 4.5f;
        m_TranRenderer.DOScaleX(m_InitScale * 0.9f, 0.1f).SetEase(Ease.OutCubic).OnComplete(() => { m_TranRenderer.DOScaleX(m_InitScale, 0.1f).SetEase(Ease.OutCubic); });
        m_TranRenderer.DOScaleZ(m_InitScale * 0.9f, 0.1f).SetEase(Ease.OutCubic).OnComplete(() => { m_TranRenderer.DOScaleZ(m_InitScale, 0.1f).SetEase(Ease.OutCubic); });
        foreach (var effect in m_Effects)
        {
            effect.Play();
        }
        m_TextHp.text = $"HP:{(int)m_CurHp}";

    }

}