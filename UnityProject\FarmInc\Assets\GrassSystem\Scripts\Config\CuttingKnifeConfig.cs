using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using Qarth;
using Animancer;

namespace GameWish.Game
{
    [CreateAssetMenu(menuName = "GameSO/LittleGame/CuttingKnifeConfig")]
    public class CuttingKnifeConfig : ScriptableObject
    {
        public List<KnifeConfig> knifeConfigs;
    }

    [System.Serializable]
    public class KnifeConfig
    {
        public int id;
        public int power = 10;
    }
}