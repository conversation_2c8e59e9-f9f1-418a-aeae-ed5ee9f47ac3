using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using Qarth;
using Animancer;

namespace GameWish.Game
{
    [CreateAssetMenu(menuName = "GameSO/Role/LittleGameRoleAnimSO")]
    public class LittleGameRoleAnimSO : ScriptableObject
    {
        [Header("AvatarMask")]
        public AvatarMask avatarMask;

        [Header("Base")]
        public AnimationClip[] idleClips;

        public AnimationClip idleClip
        {
            get { return idleClips[RandomHelper.Range(0, idleClips.Length)]; }
        }
        public AnimationClip walkClip;
        public AnimationClip runClip;
        public AnimationClip fallClip;


        public LinearMixerTransition normalMoveBlendTree;
    }
}