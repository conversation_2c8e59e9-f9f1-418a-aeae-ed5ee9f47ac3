#pragma kernel CSMain

// Thread group size
#define THREAD_GROUP_SIZE 8

// Input parameters
RWTexture2D<float> MaskTexture;
Texture2D<float4> BrushTexture;
SamplerState sampler_BrushTexture;

// Brush parameters
float2 BrushPosition;
float BrushSize;
float BrushStrength;
float BrushFalloff;
int TextureSize;

// Batch processing
StructuredBuffer<float4> DigPositions; // x,y = position, z = size, w = strength
int DigCount;

[numthreads(THREAD_GROUP_SIZE, THREAD_GROUP_SIZE, 1)]
void CSMain(uint3 id : SV_DispatchThreadID)
{
    // Check bounds
    if (id.x >= (uint)TextureSize || id.y >= (uint)TextureSize)
        return;

    // Convert thread ID to UV coordinates
    float2 uv = float2(id.xy) / float(TextureSize);
    
    // Current mask value
    float currentMask = MaskTexture[id.xy];
    
    // Process all dig positions in this batch
    for (int i = 0; i < DigCount; i++)
    {
        float4 digData = DigPositions[i];
        float2 digPos = digData.xy;
        float digSize = digData.z;
        float digStrength = digData.w;
        
        // Calculate distance from dig center
        float2 offset = uv - digPos;
        float distance = length(offset);
        
        // Calculate influence based on distance and size
        float normalizedDistance = distance / digSize;
        
        if (normalizedDistance <= 1.0)
        {
            // Apply falloff curve
            float influence = 1.0 - pow(normalizedDistance, BrushFalloff);
            
            // Sample brush texture for additional detail
            float2 brushUV = (offset / digSize) * 0.5 + 0.5;
            float brushSample = BrushTexture.SampleLevel(sampler_BrushTexture, brushUV, 0).r;
            
            // Combine brush texture with radial falloff
            float finalInfluence = influence * brushSample * digStrength;
            
            // Apply digging (multiply to darken the mask)
            currentMask *= (1.0 - finalInfluence);
        }
    }
    
    // Clamp to valid range
    currentMask = saturate(currentMask);
    
    // Write back to texture
    MaskTexture[id.xy] = currentMask;
}
