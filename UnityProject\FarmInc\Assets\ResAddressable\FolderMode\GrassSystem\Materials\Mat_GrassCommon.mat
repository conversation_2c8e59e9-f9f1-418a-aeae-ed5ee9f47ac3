%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-4513335643740521273
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0353a89b1f911e48b9e16bdc9f2e058, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 7
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Mat_GrassCommon
  m_Shader: {fileID: 4800000, guid: 4f0c9f4b574c09344a53e7a41f03dc3b, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords:
  - _OUTLINETYPE_VERTEXCOLOR
  m_InvalidKeywords:
  - __RENDERMODE_OPAQUE
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap:
    RenderType: Opaque
  disabledShaderPasses: []
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BaseMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: d846fd42929c7784ba0190f395c2d354, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MatcapMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SnowTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _ALPHATEST: 0
    - _AlphaClip: 0
    - _AlphaToMask: 0
    - _Blend: 0
    - _BlendModePreserveSpecular: 1
    - _BumpScale: 1
    - _CUTEFFECT: 0
    - _ClearCoatMask: 0
    - _ClearCoatSmoothness: 0
    - _Cull: 2
    - _Cutoff: 0.5
    - _DetailAlbedoMapScale: 1
    - _DetailNormalMapScale: 1
    - _DstBlend: 0
    - _DstBlendAlpha: 0
    - _EmissionIntensity: 0
    - _EnvironmentReflections: 1
    - _F_Advanced: 1
    - _F_Advanced_Out: 1
    - _F_Basic: 1
    - _F_Basic_Out: 1
    - _F_Outline: 1
    - _F_Outline_Out: 1
    - _F_Snow_Out: 1
    - _F_Stencil: 1
    - _F_Stencil_Out: 1
    - _F_Surface: 1
    - _F_Surface_Out: 1
    - _F_USEPBR_Out: 1
    - _GLINT: 0
    - _Glint: 0
    - _GlossMapScale: 0
    - _Glossiness: 0
    - _GlossyReflections: 0
    - _MTMapBrightness: 1
    - _MTMapTileScale: 1
    - _MTShininess: 11
    - _MTSpecularScale: 60
    - _Metallic: 0
    - _OcclusionStrength: 1
    - _Offset_Z: 0
    - _OutlineType: 1
    - _OutlineWidth: 1
    - _Parallax: 0.005
    - _QueueOffset: 0
    - _RECEIVE_SHADOWS_OFF: 0
    - _RECEIVE_SSAO_OFF: 0
    - _ReceiveShadows: 1
    - _Smoothness: 0.5
    - _SmoothnessTextureChannel: 0
    - _Snow: 0
    - _SnowDepth: 0
    - _SnowSoftness: 0
    - _SnowStrength: 0
    - _SnowTilling: 1
    - _SpecularHighlights: 1
    - _SrcBlend: 1
    - _SrcBlendAlpha: 1
    - _StencilComp: 8
    - _StencilOp: 0
    - _StencilRef: 0
    - _Surface: 0
    - _USEPBR: 0
    - _UseStencil: 0
    - _WorkflowMode: 1
    - _ZTest: 4
    - _ZWrite: 1
    - __RenderMode: 0
    m_Colors:
    - _BaseColor: {r: 1, g: 1, b: 1, a: 1}
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _GlintColor: {r: 1, g: 1, b: 1, a: 1}
    - _MTMapDarkColor: {r: 1, g: 1, b: 1, a: 1}
    - _MTMapLightColor: {r: 1, g: 1, b: 1, a: 1}
    - _MainTexColor: {r: 1, g: 1, b: 1, a: 1}
    - _OutlineColor: {r: 0, g: 0, b: 0, a: 0}
    - _PlaneNormal: {r: 0, g: 0, b: 0, a: 0}
    - _PlanePos: {r: 0, g: 0, b: 0, a: 0}
    - _SnowColor: {r: 1, g: 1, b: 1, a: 1}
    - _SpecColor: {r: 0.2, g: 0.2, b: 0.2, a: 1}
  m_BuildTextureStacks: []
