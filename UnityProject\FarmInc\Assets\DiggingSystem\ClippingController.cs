using UnityEngine;

public class ClippingController : MonoBehaviour
{
	public Material clippingMaterial;
	public Camera mainCamera;
	public float clipScale = 1f;

	void Update()
	{
		if (Input.GetMouseButtonDown(0))
		{
			Ray ray = mainCamera.ScreenPointToRay(Input.mousePosition);
			RaycastHit hit;

			if (Physics.Raycast(ray, out hit))
			{
				// 获取点击位置的世界坐标
				Vector3 worldPos = hit.point;

				// 更新材质的裁切中心
				if (clippingMaterial != null)
				{
					clippingMaterial.SetVector("_ClipCenter", new Vector4(worldPos.x, worldPos.y, 0, 0));
					clippingMaterial.SetFloat("_ClipScale", clipScale);

					Debug.Log($"裁切中心已设置为: {worldPos}");
				}
			}
		}

		// 可选：通过滚轮调整裁切大小
		float scroll = Input.GetAxis("Mouse ScrollWheel");
		if (scroll != 0)
		{
			clipScale = Mathf.Clamp(clipScale + scroll, 0.1f, 5f);
			if (clippingMaterial != null)
			{
				clippingMaterial.SetFloat("_ClipScale", clipScale);
			}
		}
	}
}