Shader "URP/DiggingBrush"
{
    Properties
    {
        _BrushTex("Brush Texture", 2D) = "white" {}
        _BrushPos("Brush Position", Vector) = (0.5, 0.5, 0, 0)
        _BrushSize("Brush Size", Float) = 0.1
        _BrushStrength("Brush Strength", Range(0, 1)) = 1.0
        _Falloff("Falloff", Range(0.1, 2.0)) = 1.0
    }

    SubShader
    {
        Tags
        {
            "RenderType" = "Transparent"
            "RenderPipeline" = "UniversalPipeline"
            "Queue" = "Transparent"
        }

        Pass
        {
            Name "DiggingBrush"
            
            Blend Zero SrcColor  // Multiplicative blend to darken the mask
            ZWrite Off
            ZTest Always
            Cull Off

            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #pragma target 4.5

            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"

            struct Attributes
            {
                float4 positionOS : POSITION;
                float2 uv : TEXCOORD0;
            };

            struct Varyings
            {
                float4 positionCS : SV_POSITION;
                float2 uv : TEXCOORD0;
                float2 screenUV : TEXCOORD1;
            };

            TEXTURE2D(_BrushTex);
            SAMPLER(sampler_BrushTex);

            CBUFFER_START(UnityPerMaterial)
                float4 _BrushTex_ST;
                float4 _BrushPos;
                float _BrushSize;
                float _BrushStrength;
                float _Falloff;
            CBUFFER_END

            Varyings vert(Attributes input)
            {
                Varyings output;
                
                output.positionCS = TransformObjectToHClip(input.positionOS.xyz);
                output.uv = TRANSFORM_TEX(input.uv, _BrushTex);
                
                // Convert to screen UV coordinates
                output.screenUV = (output.positionCS.xy / output.positionCS.w) * 0.5 + 0.5;
                
                return output;
            }

            half4 frag(Varyings input) : SV_Target
            {
                // Calculate distance from brush center
                float2 brushCenter = _BrushPos.xy;
                float2 pixelPos = input.screenUV;
                
                float distance = length(pixelPos - brushCenter);
                
                // Calculate brush influence based on distance and size
                float normalizedDistance = distance / _BrushSize;
                
                // Apply falloff curve
                float influence = 1.0 - saturate(pow(normalizedDistance, _Falloff));
                
                // Sample brush texture for additional detail
                half4 brushSample = SAMPLE_TEXTURE2D(_BrushTex, sampler_BrushTex, input.uv);
                
                // Combine brush texture with radial falloff
                float finalInfluence = influence * brushSample.r * _BrushStrength;
                
                // Return the mask value (1 = no dig, 0 = full dig)
                float maskValue = 1.0 - finalInfluence;
                
                return half4(maskValue, maskValue, maskValue, 1.0);
            }
            ENDHLSL
        }
    }
    
    Fallback Off
}
