using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
using Sirenix.OdinInspector;
public class TestDigging : MonoBehaviour
{
	private enum pointStatic
	{
		die,                        //已经废弃
		wait,                       //等待检测
		collid                      //正在使用
	}

	private class PointData
	{
		public Vector2 pos;               //点位置
		public pointStatic sta;          //点状态
		public GameObject col;          //点实例
	}

	private Vector2 scale = new Vector2(1, 1);
	private List<PointData> listPoint = new List<PointData>();
	public GameObject target;
	public float ColliderSize = 0.1f;
	public float Circle1Range1 = 1f;
	public float Circle1Range2 = 2f;
	public Camera mainCam;

	private void Start()
	{
		CreatePoint();
	}

	[Button("创建点")]
	private void CreatePoint()
	{
		int line = (int)(scale.x * 100);
		int row = (int)(scale.y * 100);
		float distancesX = 1f / line;
		float distancesY = 1f / row;
		for (int j = 0; j < row; j++)
		{
			for (int k = 0; k < line; k++)
			{
				Vector2 v2 = new Vector2(distancesX * k - 0.5f, distancesY * j - 0.5f);
				PointData data = new PointData();
				data.pos = v2;
				data.sta = pointStatic.wait;
				listPoint.Add(data);
			}
		}
		distancesX *= 3;
		distancesY *= 3;
		for (float x = -0.5f; x <= 0.5; x += distancesX)
		{
			CreateSideCollider(new Vector3(x, -0.5f, 0));
			CreateSideCollider(new Vector3(x, 0.5f, 0));
			CreateSideCollider(new Vector3(-0.5f, x, 0));
			CreateSideCollider(new Vector3(0.5f, x, 0));
		}
	}

	public void CreateSideCollider(Vector3 v3)
	{
		Object obj = Resources.Load("MousePoint3");
		GameObject go = Instantiate(obj, Vector2.zero, Quaternion.Euler(Vector3.zero), target.transform) as GameObject;
		go.transform.localPosition = v3;
		go.transform.localScale = new Vector3(ColliderSize / scale.x, ColliderSize / scale.y, ColliderSize);
		PointData data = new PointData();
		data.pos = new Vector2(v3.x, v3.y);
		data.sta = pointStatic.collid;
		data.col = go;
		listPoint.Add(data);
	}

	private void Update()
	{
		if (Input.GetMouseButton(0))
		{
			WaDong();
		}
	}

	public void WaDong()
	{
		//创建一条射线一摄像机为原点
		Ray ray = mainCam.ScreenPointToRay(Input.mousePosition);
		RaycastHit hit;
		//射线碰撞到游戏地形时
		if (Physics.Raycast(ray, out hit))
		{
			//从世界坐标转为局部坐标
			Vector2 localCenter = target.transform.InverseTransformPoint(hit.point);
			for (int i = 0; i < listPoint.Count; i++)
			{
				Vector2 centerPos = listPoint[i].pos - localCenter;
				centerPos.x *= scale.x;
				centerPos.y *= scale.y;
				float dis = Vector2.Distance(centerPos, Vector2.zero);
				if (dis < Circle1Range1 && listPoint[i].sta != pointStatic.die)
				{
					listPoint[i].sta = pointStatic.die;
					if (listPoint[i].col != null)
					{
						GameObject.Destroy(listPoint[i].col);
					}

				}
				else if (dis >= Circle1Range1 && dis < Circle1Range2 && listPoint[i].sta == pointStatic.wait)
				{
					listPoint[i].sta = pointStatic.collid;
					Object obj = Resources.Load("MousePoint3");
					GameObject go = Instantiate(obj, Vector2.zero, Quaternion.Euler(Vector3.zero), target.transform) as GameObject;
					go.transform.localPosition = listPoint[i].pos;
					go.transform.localScale = new Vector3(ColliderSize / scale.x, ColliderSize / scale.y, ColliderSize);
					listPoint[i].col = go;
				}
			}
		}
	}
}
