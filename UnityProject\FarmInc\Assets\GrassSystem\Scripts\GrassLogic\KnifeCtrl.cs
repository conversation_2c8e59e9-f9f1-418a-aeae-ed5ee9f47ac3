using System;
using System.Collections.Generic;
using GameWish.Game;
using Qarth;
using UnityEngine;

public class KnifeCtrl : MonoBehaviour
{
    public Transform target;
    public CuttingKnifeConfig cuttingKnifeConfig;
    public Dictionary<int, CuttingKnife> m_DicPreCuttingKnife = new Dictionary<int, CuttingKnife>();
    public List<CuttingKnife> m_CurCuttingKnives = new List<CuttingKnife>();
    public float speed = 1f;
    public float radius = 1f;
    public int knifeCount = 1;
    public int realKnifeCount = 1;
    public int knifeId = 1;
    public int lastKnifeId = -1;
    public int power = 10;
    public List<GrassLand> mowGrass;

    private bool m_AsOne = false;

    private void Start()
    {
        mowGrass = GrassMgr.S.grassLands;

        ChangeKnifeCount();
    }

    public void Stop()
    {
        for (int i = 0; i < m_CurCuttingKnives.Count; i++)
        {
            Destroy(m_CurCuttingKnives[i].gameObject);
        }
        m_CurCuttingKnives.Clear();
    }

    public void Reset()
    {
        knifeCount = 1;
        knifeId = 1;
        lastKnifeId = -1;
        for (int i = 0; i < m_CurCuttingKnives.Count; i++)
        {
            Destroy(m_CurCuttingKnives[i].gameObject);
        }
        m_CurCuttingKnives.Clear();
        ChangeKnifeCount();
    }

    private int GetKnifePower(int id)
    {
        return cuttingKnifeConfig.knifeConfigs.Find(config => config.id == id).power;
    }

    public void GetCuttingKnife(int id, Action<CuttingKnife> callback)
    {
        if (m_DicPreCuttingKnife.ContainsKey(id))
        {
            callback.Invoke(m_DicPreCuttingKnife[id]);
        }
        else
        {
            AddressableResMgr.S.LoadAssetAsyncByName<GameObject>("Pre_Knife" + id, (obj, state) =>
            {
                if (state)
                {
                    var knife = obj.GetComponent<CuttingKnife>();
                    if (!m_DicPreCuttingKnife.ContainsKey(id))
                    {
                        m_DicPreCuttingKnife.Add(id, knife);
                    }
                    callback.Invoke(m_DicPreCuttingKnife[id]);
                }
            });
        }
    }

    public void ChangeKnifeCount()
    {
        if (knifeCount < 0)
        {
            knifeCount = 0;
        }

        power = GetKnifePower(knifeId) * knifeCount;
        m_AsOne = knifeCount > 5;

        //根据数量计算位置，以物体为圆心，平均分配角度

        for (int i = 0; i < m_CurCuttingKnives.Count; i++)
        {
            m_CurCuttingKnives[i].gameObject.SetActive(false);
        }
        int spawnKnifeCount = knifeCount;
        if (knifeCount > 20)
        {
            spawnKnifeCount = 20;
        }
        float angle = 360f / spawnKnifeCount;
        for (int i = 0; i < spawnKnifeCount; i++)
        {
            float radian = angle * i * Mathf.Deg2Rad;
            Vector3 pos = new Vector3(Mathf.Cos(radian), 0.3f, Mathf.Sin(radian)) * radius;
            Vector3 direction = new Vector3(Mathf.Cos(radian), 0, Mathf.Sin(radian));
            if (i < m_CurCuttingKnives.Count)
            {
                m_CurCuttingKnives[i].transform.localPosition = pos;
                m_CurCuttingKnives[i].transform.localRotation = Quaternion.LookRotation(direction);
                m_CurCuttingKnives[i].gameObject.SetActive(true);
                m_CurCuttingKnives[i].Init(m_AsOne, target);
            }
            else
            {
                GetCuttingKnife(knifeId, (knife) =>
                {
                    var newKnife = Instantiate(knife, transform);
                    newKnife.gameObject.SetActive(true);
                    newKnife.transform.localPosition = pos;
                    newKnife.transform.localRotation = Quaternion.LookRotation(direction);
                    m_CurCuttingKnives.Add(newKnife);
                    newKnife.Init(m_AsOne, target);
                });
            }
        }

    }

    public void ChangeKnifeRender()
    {
        if (knifeId >= 7)
        {
            knifeId = 7;
        }
        if (lastKnifeId == knifeId)
        {
            return;
        }
        lastKnifeId = knifeId;
        if (knifeCount < 0)
        {
            knifeCount = 0;
        }
        m_AsOne = knifeCount > 5;
        power = GetKnifePower(knifeId) * knifeCount;
        //根据数量计算位置，以物体为圆心，平均分配角度
        float angle = 360f / knifeCount;
        for (int i = 0; i < m_CurCuttingKnives.Count; i++)
        {
            Destroy(m_CurCuttingKnives[i].gameObject);
        }
        m_CurCuttingKnives.Clear();

        for (int i = 0; i < knifeCount; i++)
        {
            float radian = angle * i * Mathf.Deg2Rad;
            Vector3 pos = new Vector3(Mathf.Cos(radian), 0.3f, Mathf.Sin(radian)) * radius;
            Vector3 direction = new Vector3(Mathf.Cos(radian), 0, Mathf.Sin(radian));
            GetCuttingKnife(knifeId, (knife) =>
            {
                var newKnife = Instantiate(knife, transform);
                newKnife.gameObject.SetActive(true);
                newKnife.transform.localPosition = pos;
                newKnife.transform.localRotation = Quaternion.LookRotation(direction);
                m_CurCuttingKnives.Add(newKnife);
                newKnife.Init(m_AsOne, target);
            });
        }

    }

    // Update is called once per frame
    void FixedUpdate()
    {
        transform.position = target.position;
        //以目标为圆心，绕着目标旋转
        transform.localEulerAngles += new Vector3(0, speed * Time.fixedDeltaTime, 0);

        if (m_AsOne)
        {
            mowGrass.ForEach(land => land.MakeGrassDisappearInArea(transform.position, radius + 0.6f));
        }
    }
}
