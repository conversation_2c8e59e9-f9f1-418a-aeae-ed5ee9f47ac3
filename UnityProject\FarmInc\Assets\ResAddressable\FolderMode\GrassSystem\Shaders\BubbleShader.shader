Shader "URP/BubbleShader"
{
    Properties
    {
        _BaseColor ("Base Color", Color) = (0.8, 0.9, 1.0, 0.3)
        _FresnelPower ("Fresnel Power", Range(0, 10)) = 3.0
        _FresnelStrength ("Fresnel Strength", Range(0, 5)) = 1.5
        _IridescenceIntensity ("彩虹色强度", Range(0, 2)) = 0.8
        _IridescenceScale ("彩虹色缩放", Range(0, 10)) = 5.0
        _IridescenceSpeed ("彩虹色速度", Range(0, 1)) = 0.1
        _NoiseScale ("噪波缩放", Range(0, 20)) = 5.0
        _NoiseStrength ("噪波强度", Range(0, 0.1)) = 0.02
        _NoiseSpeed ("噪波速度", Range(0, 1)) = 0.1
    }

    SubShader
    {
        Tags
        {
            "RenderType" = "Transparent"
            "Queue" = "Transparent"
            "RenderPipeline" = "UniversalPipeline"
        }
        
        LOD 100

        // 深度写入关闭，确保透明物体正确排序
        ZWrite Off
        // 使用标准透明混合模式
        Blend SrcAlpha OneMinusSrcAlpha

        Pass
        {
            Name "ForwardLit"
            Tags { "LightMode" = "UniversalForward" }

            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #pragma multi_compile _ _MAIN_LIGHT_SHADOWS
            #pragma multi_compile _ _MAIN_LIGHT_SHADOWS_CASCADE
            #pragma multi_compile _ _SHADOWS_SOFT

            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Lighting.hlsl"

            struct Attributes
            {
                float4 positionOS : POSITION;
                float3 normalOS : NORMAL;
                float2 uv : TEXCOORD0;
            };

            struct Varyings
            {
                float4 positionHCS : SV_POSITION;
                float3 normalWS : TEXCOORD0;
                float3 viewDirWS : TEXCOORD1;
                float3 positionWS : TEXCOORD2;
                float2 uv : TEXCOORD3;
            };

            CBUFFER_START(UnityPerMaterial)
                float4 _BaseColor;
                float _FresnelPower;
                float _FresnelStrength;
                float _IridescenceIntensity;
                float _IridescenceScale;
                float _IridescenceSpeed;
                float _NoiseScale;
                float _NoiseStrength;
                float _NoiseSpeed;
            CBUFFER_END

            // 简单的噪声函数，用于创建泡泡表面的微小变化
            float noise(float2 uv)
            {
                return frac(sin(dot(uv, float2(12.9898, 78.233))) * 43758.5453);
            }

            Varyings vert(Attributes IN)
            {
                Varyings OUT;
                
                // 顶点动画 - 添加一些微小的扰动使泡泡表面看起来更自然
                float noiseValue = noise(IN.uv + _Time.y * _NoiseSpeed);
                float3 offset = IN.normalOS * noiseValue * _NoiseStrength;
                IN.positionOS.xyz += offset;
                
                VertexPositionInputs vertexInput = GetVertexPositionInputs(IN.positionOS.xyz);
                VertexNormalInputs normalInput = GetVertexNormalInputs(IN.normalOS);
                
                OUT.positionHCS = vertexInput.positionCS;
                OUT.normalWS = normalInput.normalWS;
                OUT.viewDirWS = GetWorldSpaceNormalizeViewDir(vertexInput.positionWS);
                OUT.positionWS = vertexInput.positionWS;
                OUT.uv = IN.uv;
                
                return OUT;
            }

            half4 frag(Varyings IN) : SV_Target
            {
                // 计算光照
                Light mainLight = GetMainLight(TransformWorldToShadowCoord(IN.positionWS));
                float3 lightColor = mainLight.color * mainLight.shadowAttenuation * mainLight.distanceAttenuation;
                
                // 法线处理
                float3 normalWS = normalize(IN.normalWS);
                float3 viewDirWS = normalize(IN.viewDirWS);
                
                // 菲涅尔效应 - 边缘更亮
                float fresnel = pow(1.0 - saturate(dot(normalWS, viewDirWS)), _FresnelPower);
                float3 fresnelColor = fresnel * _FresnelStrength * lightColor;
                
                // 彩虹色效果 - 模拟泡泡表面的薄膜干涉
                float iridescence = sin(IN.uv.x * _IridescenceScale + _Time.y * _IridescenceSpeed) * 0.5 + 0.5;
                iridescence += sin(IN.uv.y * _IridescenceScale * 0.7 + _Time.y * _IridescenceSpeed * 1.3) * 0.5 + 0.5;
                iridescence *= 0.5;
                
                float3 rainbowColor = float3(
                    abs(sin(iridescence * 3.14159 * 2.0 + 0.0)),
                    abs(sin(iridescence * 3.14159 * 2.0 + 2.094)),
                    abs(sin(iridescence * 3.14159 * 2.0 + 4.188))
                ) * _IridescenceIntensity;
                
                // 组合所有效果
                float3 finalColor = _BaseColor.rgb + fresnelColor + rainbowColor;
                
                // 根据视角调整透明度 - 中心更透明，边缘更不透明
                float alpha = _BaseColor.a * (1.0 - fresnel * 0.5);
                
                return half4(finalColor, alpha);
            }
            ENDHLSL
        }
        
        // 阴影投射Pass，确保泡泡能投射阴影
        Pass
        {
            Name "ShadowCaster"
            Tags{"LightMode" = "ShadowCaster"}
            
            ZWrite On
            ZTest LEqual
            ColorMask 0
            Cull Back
            
            HLSLPROGRAM
            #pragma vertex ShadowPassVertex
            #pragma fragment ShadowPassFragment
            
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Lighting.hlsl"
            
            float3 _LightDirection;
            
            struct Attributes
            {
                float4 positionOS : POSITION;
                float3 normalOS : NORMAL;
                float2 texcoord : TEXCOORD0;
            };
            
            struct Varyings
            {
                float4 positionCS : SV_POSITION;
                float2 uv : TEXCOORD0;
            };
            
            Varyings ShadowPassVertex(Attributes v)
            {
                Varyings o;
                
                float3 positionWS = TransformObjectToWorld(v.positionOS.xyz);
                float3 normalWS = TransformObjectToWorldNormal(v.normalOS);
                
                float4 positionCS = TransformWorldToHClip(ApplyShadowBias(positionWS, normalWS, _LightDirection));
                
                #if UNITY_REVERSED_Z
                    positionCS.z = min(positionCS.z, positionCS.w * UNITY_NEAR_CLIP_VALUE);
                #else
                    positionCS.z = max(positionCS.z, positionCS.w * UNITY_NEAR_CLIP_VALUE);
                #endif
                
                o.positionCS = positionCS;
                o.uv = v.texcoord;
                
                return o;
            }
            
            half4 ShadowPassFragment(Varyings i) : SV_TARGET
            {
                return 0;
            }
            
            ENDHLSL
        }
    }
    
    FallBack "Universal Render Pipeline/Simple Lit"
}